from django import forms
from .models import (
    Pet, PetGallery, PetMedicalRecord, Vaccination, GrowthRecord,
    Milestone, PetReminder, ExerciseLog, FeedingLog, FeedingSchedule
)


class PetForm(forms.ModelForm):
    """Form for creating and updating pets"""
    class Meta:
        model = Pet
        fields = ('name', 'category', 'breed', 'birth_date', 'gender',
                  'profile_picture', 'bio', 'is_for_adoption', 'adoption_price')
        widgets = {
            'birth_date': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make breed field dependent on selected category
        if 'category' in self.data:
            try:
                category_id = int(self.data.get('category'))
                self.fields['breed'].queryset = self.fields['breed'].queryset.filter(category_id=category_id)
            except (<PERSON><PERSON><PERSON><PERSON>, TypeError):
                pass
        elif self.instance.pk and self.instance.category:
            self.fields['breed'].queryset = self.fields['breed'].queryset.filter(category=self.instance.category)


class PetGalleryForm(forms.ModelForm):
    """Form for adding photos to pet gallery"""
    class Meta:
        model = PetGallery
        fields = ('image', 'caption')


class PetMedicalRecordForm(forms.ModelForm):
    """Form for adding medical records to pets"""
    class Meta:
        model = PetMedicalRecord
        fields = ('record_date', 'record_type', 'description', 'veterinarian', 'clinic', 'document')
        widgets = {
            'record_date': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 4}),
        }


class VaccinationForm(forms.ModelForm):
    """Form for adding vaccination records"""
    class Meta:
        model = Vaccination
        fields = ('vaccine_type', 'vaccine_name', 'date_administered', 'next_due_date',
                  'batch_number', 'veterinarian', 'clinic', 'notes', 'certificate')
        widgets = {
            'date_administered': forms.DateInput(attrs={'type': 'date'}),
            'next_due_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }


class GrowthRecordForm(forms.ModelForm):
    """Form for adding growth records"""
    class Meta:
        model = GrowthRecord
        fields = ('date_recorded', 'weight', 'height', 'length', 'notes', 'photo')
        widgets = {
            'date_recorded': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
            'weight': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'height': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'length': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
        }


class MilestoneForm(forms.ModelForm):
    """Form for adding milestones"""
    class Meta:
        model = Milestone
        fields = ('milestone_type', 'title', 'description', 'date_achieved',
                  'age_at_milestone', 'photo', 'video')
        widgets = {
            'date_achieved': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 4}),
        }


class PetReminderForm(forms.ModelForm):
    """Form for adding reminders"""
    class Meta:
        model = PetReminder
        fields = ('reminder_type', 'title', 'description', 'due_date', 'frequency',
                  'is_recurring', 'notification_enabled')
        widgets = {
            'due_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'description': forms.Textarea(attrs={'rows': 3}),
        }


class ExerciseLogForm(forms.ModelForm):
    """Form for logging exercise activities"""
    class Meta:
        model = ExerciseLog
        fields = ('date', 'activity_type', 'duration_minutes', 'intensity',
                  'distance', 'location', 'notes', 'weather')
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
            'duration_minutes': forms.NumberInput(attrs={'min': '1'}),
            'distance': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
        }


class FeedingLogForm(forms.ModelForm):
    """Form for logging feeding activities"""
    class Meta:
        model = FeedingLog
        fields = ('date_time', 'meal_type', 'food_type', 'amount_given',
                  'amount_consumed', 'notes')
        widgets = {
            'date_time': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

{% extends 'base.html' %}

{% block title %}{{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .pet-detail-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--gap-2xl);
    }

    @media (max-width: 992px) {
        .pet-detail-container {
            grid-template-columns: 1fr;
        }
    }

    .pet-gallery {
        position: relative;
    }

    .pet-main-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-base);
    }

    .pet-thumbnails {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--gap-sm);
    }

    .pet-thumbnail {
        width: 100%;
        height: 80px;
        object-fit: cover;
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: var(--transition-base);
    }

    .pet-thumbnail:hover {
        opacity: 0.8;
    }

    .pet-thumbnail.active {
        border: 2px solid var(--primary);
    }

    .pet-info {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }

    .pet-name {
        font-size: var(--font-3xl);
        margin-bottom: var(--spacing-xs);
    }

    .pet-breed {
        color: var(--text-light);
        margin-bottom: var(--spacing-lg);
        font-size: var(--font-lg);
    }

    .pet-meta {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: var(--gap-base);
        margin-bottom: var(--spacing-xl);
        padding-bottom: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .pet-meta-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .pet-meta-icon {
        font-size: var(--font-xl);
        color: var(--primary);
        margin-bottom: var(--spacing-xs);
    }

    .pet-meta-label {
        font-size: var(--font-sm);
        color: var(--text-light);
    }

    .pet-meta-value {
        font-weight: var(--fw-medium);
    }

    .pet-bio {
        margin-bottom: var(--spacing-xl);
    }

    .pet-bio-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
    }

    .pet-adoption {
        background-color: var(--primary-light);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    .pet-adoption-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
        color: var(--primary-dark);
    }

    .pet-adoption-price {
        font-size: var(--font-2xl);
        font-weight: var(--fw-bold);
        color: var(--primary);
        margin-bottom: var(--spacing-base);
    }

    .pet-actions {
        display: flex;
        gap: var(--gap-xl);
    }

    .pet-owner {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        margin-top: var(--spacing-xl);
        padding-top: var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
    }

    .pet-owner-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .pet-owner-info {
        flex: 1;
    }

    .pet-owner-name {
        font-weight: var(--fw-medium);
    }

    .pet-owner-joined {
        font-size: var(--font-sm);
        color: var(--text-light);
    }

    .pet-tabs {
        margin-top: var(--spacing-3xl);
    }

    .tab-list {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid var(--gray-200);
        margin-bottom: var(--spacing-xl);
    }

    .tab-item {
        margin-right: var(--spacing-xl);
    }

    .tab-link {
        display: block;
        padding: var(--spacing-base) 0;
        color: var(--text-light);
        font-weight: var(--fw-medium);
        position: relative;
    }

    .tab-link.active {
        color: var(--primary);
    }

    .tab-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--primary);
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    .medical-records {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    .medical-record {
        padding: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .medical-record:last-child {
        border-bottom: none;
    }

    .medical-record-date {
        font-size: var(--font-sm);
        color: var(--text-light);
        margin-bottom: var(--spacing-xs);
    }

    .medical-record-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-sm);
    }

    .medical-record-vet {
        font-size: var(--font-sm);
        color: var(--text-light);
        margin-bottom: var(--spacing-base);
    }

    /* Pet Inquiry Modal Styles */
    .pet-inquiry-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .pet-inquiry-modal.show {
        display: flex;
    }

    .pet-inquiry-modal .modal-content {
        background: var(--white);
        border-radius: var(--radius-lg);
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: var(--shadow-lg);
    }

    .pet-inquiry-modal .modal-header {
        padding: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .pet-inquiry-modal .modal-title {
        margin: 0;
        font-size: var(--font-lg);
        color: var(--gray-900);
    }

    .pet-inquiry-modal .modal-close {
        background: none;
        border: none;
        font-size: var(--font-lg);
        color: var(--gray-500);
        cursor: pointer;
        padding: var(--spacing-xs);
        border-radius: var(--radius-base);
        transition: var(--transition-base);
    }

    .pet-inquiry-modal .modal-close:hover {
        background-color: var(--gray-100);
        color: var(--gray-700);
    }

    .pet-inquiry-modal .modal-body {
        padding: var(--spacing-xl);
    }

    .pet-inquiry-preview {
        display: flex;
        gap: var(--gap-base);
        align-items: center;
        margin-bottom: var(--spacing-xl);
        padding: var(--spacing-base);
        background-color: var(--gray-50);
        border-radius: var(--radius-md);
    }

    .inquiry-pet-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: var(--radius-md);
    }

    .inquiry-pet-info h4 {
        margin: 0 0 var(--spacing-xs) 0;
        color: var(--gray-900);
    }

    .inquiry-pet-info p {
        margin: 0 0 var(--spacing-xs) 0;
        color: var(--gray-600);
        font-size: var(--font-sm);
    }

    .inquiry-price {
        font-weight: 600;
        color: var(--primary);
        font-size: var(--font-lg);
    }

    .inquiry-message-preview h5 {
        margin: 0 0 var(--spacing-base) 0;
        color: var(--gray-900);
        font-size: var(--font-base);
    }

    .message-preview {
        background-color: var(--gray-50);
        padding: var(--spacing-base);
        border-radius: var(--radius-md);
        border-left: 4px solid var(--primary);
        margin-bottom: var(--spacing-xl);
        line-height: 1.5;
        color: var(--gray-700);
    }

    .inquiry-note {
        font-size: var(--font-sm);
        color: var(--gray-600);
        margin: 0;
        text-align: center;
    }

    .pet-inquiry-modal .modal-footer {
        padding: var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
        display: flex;
        gap: var(--gap-base);
        justify-content: flex-end;
    }

    /* Wishlist Button Styles */
    .btn-wishlist {
        background-color: transparent;
        border: 2px solid var(--primary);
        color: var(--primary);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-wishlist:hover {
        background-color: var(--primary);
        color: white;
        transform: translateY(-2px);
    }

    .btn-wishlist.in-wishlist {
        background-color: var(--primary);
        color: white;
    }

    .btn-wishlist.in-wishlist:hover {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .btn-wishlist i {
        transition: transform 0.2s ease;
    }

    .btn-wishlist:hover i {
        transform: scale(1.1);
    }

    /* Owner Dashboard Styles */
    .urgency-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .urgency-low { background: var(--success-light); color: var(--success-dark); }
    .urgency-medium { background: var(--warning-light); color: var(--warning-dark); }
    .urgency-high { background: var(--danger-light); color: var(--danger-dark); }
    .urgency-emergency { background: var(--danger-color); color: white; }

    .medical-record-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .health-dashboard, .growth-dashboard, .reminders-dashboard {
        padding: var(--spacing-lg);
    }

    .health-alerts {
        margin-bottom: var(--spacing-xl);
    }

    .alert {
        padding: var(--spacing-lg);
        border-radius: var(--border-radius);
        margin-bottom: var(--spacing-base);
    }

    .alert-danger {
        background: var(--danger-light);
        border: 1px solid var(--danger-color);
        color: var(--danger-dark);
    }

    .alert h4 {
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .alert-item {
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }

    .alert-item:last-child {
        border-bottom: none;
    }

    .health-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .health-stat-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-base);
    }

    .stat-icon {
        width: 3rem;
        height: 3rem;
        background: var(--primary-light);
        color: var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }

    .stat-value {
        font-size: var(--font-xl);
        font-weight: 700;
        color: var(--text-primary);
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: var(--font-sm);
    }

    .growth-records {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-base);
        margin-bottom: var(--spacing-xl);
    }

    .growth-record {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: var(--spacing-lg);
    }

    .growth-date {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: var(--spacing-sm);
    }

    .growth-weight {
        font-size: var(--font-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-xs);
    }

    .growth-height, .growth-notes {
        color: var(--text-secondary);
        font-size: var(--font-sm);
    }

    .milestones {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-base);
        margin-bottom: var(--spacing-xl);
    }

    .milestone {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: var(--spacing-lg);
    }

    .milestone-type {
        background: var(--primary-light);
        color: var(--primary-color);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: var(--font-sm);
        font-weight: 500;
        display: inline-block;
        margin-bottom: var(--spacing-sm);
    }

    .milestone-title {
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    .milestone-date, .milestone-age {
        color: var(--text-secondary);
        font-size: var(--font-sm);
    }

    .reminder-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-base);
    }

    .reminder-card.overdue {
        border-left: 4px solid var(--danger-color);
        background: var(--danger-light);
    }

    .reminder-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-sm);
    }

    .reminder-title {
        font-weight: 600;
        color: var(--text-primary);
    }

    .reminder-date {
        color: var(--text-secondary);
        font-size: var(--font-sm);
    }

    .reminder-type {
        color: var(--primary-color);
        font-size: var(--font-sm);
        font-weight: 500;
        margin-bottom: var(--spacing-sm);
    }

    .reminder-description {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-base);
    }

    .reminder-actions {
        display: flex;
        gap: var(--spacing-sm);
    }

    .quick-actions {
        display: flex;
        gap: var(--spacing-base);
        flex-wrap: wrap;
        margin-top: var(--spacing-xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="pet-detail-container">
        <div class="pet-gallery">
            <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="pet-main-image" id="main-image">

            <div class="pet-thumbnails">
                <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="pet-thumbnail active" data-src="{{ pet.profile_picture.url }}">

                {% for photo in pet.gallery.all %}
                    <img src="{{ photo.image.url }}" alt="{{ pet.name }}" class="pet-thumbnail" data-src="{{ photo.image.url }}">
                {% endfor %}
            </div>
        </div>

        <div class="pet-info">
            <h1 class="pet-name">{{ pet.name }}</h1>
            <p class="pet-breed">{{ pet.breed.name }}</p>

            <div class="pet-meta">
                <div class="pet-meta-item">
                    <div class="pet-meta-icon">
                        <i class="fas fa-venus-mars"></i>
                    </div>
                    <div class="pet-meta-label">Gender</div>
                    <div class="pet-meta-value">{{ pet.get_gender_display }}</div>
                </div>

                <div class="pet-meta-item">
                    <div class="pet-meta-icon">
                        <i class="fas fa-birthday-cake"></i>
                    </div>
                    <div class="pet-meta-label">Age</div>
                    <div class="pet-meta-value">{{ pet.birth_date|timesince }}</div>
                </div>

                <div class="pet-meta-item">
                    <div class="pet-meta-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="pet-meta-label">Location</div>
                    <div class="pet-meta-value">{{ pet.owner.location|default:"Not specified" }}</div>
                </div>
            </div>

            <div class="pet-bio">
                <h2 class="pet-bio-title">About {{ pet.name }}</h2>
                <p>{{ pet.bio }}</p>
            </div>

            {% if pet.is_for_adoption %}
                <div class="pet-adoption">
                    <h3 class="pet-adoption-title">Available for Adoption</h3>
                    {% if pet.adoption_price %}
                        <div class="pet-adoption-price">${{ pet.adoption_price }}</div>
                    {% endif %}
                    <p>This pet is looking for a loving home. Contact the owner for more information.</p>
                </div>
            {% endif %}

            <div class="pet-actions">
                {% if user.is_authenticated and user != pet.owner %}
                    {% if is_following %}
                        <a href="{% url 'follow-pet' pk=pet.pk %}" class="btn btn-outline">Unfollow</a>
                    {% else %}
                        <a href="{% url 'follow-pet' pk=pet.pk %}" class="btn btn-primary">Follow</a>
                    {% endif %}

                    <button type="button" class="btn btn-wishlist" id="wishlist-btn" data-item-id="{{ pet.id }}" data-item-type="pet">
                        <i class="fas fa-heart"></i>
                        <span class="wishlist-text">Add to Wishlist</span>
                    </button>

                    <button type="button" class="btn btn-secondary" id="pet-inquiry-btn" data-pet-id="{{ pet.id }}">
                        {% if pet.is_for_adoption %}
                            Inquire About Adoption
                        {% else %}
                            Contact About Pet
                        {% endif %}
                    </button>
                {% elif user == pet.owner %}
                    <a href="{% url 'pet-update' pk=pet.pk %}" class="btn btn-primary">Edit Pet</a>
                    <a href="{% url 'add-pet-photo' pk=pet.pk %}" class="btn btn-outline">Add Photo</a>
                    <a href="{% url 'add-medical-record' pk=pet.pk %}" class="btn btn-outline">Add Medical Record</a>
                {% endif %}
            </div>

            <div class="pet-owner">
                <img src="{{ pet.owner.profile_picture.url }}" alt="{{ pet.owner.username }}" class="pet-owner-avatar">

                <div class="pet-owner-info">
                    <div class="pet-owner-name">{{ pet.owner.username }}</div>
                    <div class="pet-owner-joined">Member since {{ pet.owner.date_joined|date:"F Y" }}</div>
                </div>

                <a href="{% url 'user-profile' username=pet.owner.username %}" class="btn btn-sm btn-outline">View Profile</a>
            </div>
        </div>
    </div>

    <div class="pet-tabs">
        <ul class="tab-list">
            <li class="tab-item">
                <a href="#photos" class="tab-link active">Photos</a>
            </li>
            <li class="tab-item">
                <a href="#medical" class="tab-link">Medical Records</a>
            </li>
            {% if is_owner %}
                <li class="tab-item">
                    <a href="#health" class="tab-link">Health Tracking</a>
                </li>
                <li class="tab-item">
                    <a href="#growth" class="tab-link">Growth & Milestones</a>
                </li>
                <li class="tab-item">
                    <a href="#reminders" class="tab-link">Reminders</a>
                </li>
            {% endif %}
        </ul>

        <div class="tab-content">
            <div id="photos" class="tab-pane active">
                <div class="pet-photos-grid">
                    <!-- Photos will be displayed here -->
                </div>
            </div>

            <div id="medical" class="tab-pane">
                {% if recent_medical_records %}
                    <div class="medical-records">
                        {% for record in recent_medical_records %}
                            <div class="medical-record">
                                <div class="medical-record-header">
                                    <div class="medical-record-date">{{ record.record_date|date:"F j, Y" }}</div>
                                    <span class="urgency-badge urgency-{{ record.urgency }}">{{ record.get_urgency_display }}</span>
                                </div>
                                <h3 class="medical-record-title">{{ record.title }}</h3>
                                <div class="medical-record-type">{{ record.get_record_type_display }}</div>
                                {% if record.veterinarian %}
                                    <div class="medical-record-vet">Veterinarian: {{ record.veterinarian }}</div>
                                {% endif %}
                                {% if record.clinic %}
                                    <div class="medical-record-clinic">Clinic: {{ record.clinic }}</div>
                                {% endif %}
                                <p class="medical-record-description">{{ record.description }}</p>
                                {% if record.diagnosis %}
                                    <div class="medical-record-diagnosis">
                                        <strong>Diagnosis:</strong> {{ record.diagnosis }}
                                    </div>
                                {% endif %}
                                {% if record.treatment %}
                                    <div class="medical-record-treatment">
                                        <strong>Treatment:</strong> {{ record.treatment }}
                                    </div>
                                {% endif %}
                                {% if record.follow_up_date %}
                                    <div class="medical-record-followup">
                                        <strong>Follow-up:</strong> {{ record.follow_up_date|date:"F j, Y" }}
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <p>No medical records available.</p>
                        {% if is_owner %}
                            <a href="{% url 'add-medical-record' pk=pet.pk %}" class="btn btn-primary">Add Medical Record</a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>

            {% if is_owner %}
                <!-- Health Tracking Tab -->
                <div id="health" class="tab-pane">
                    <div class="health-dashboard">
                        <div class="health-alerts">
                            {% if overdue_vaccinations %}
                                <div class="alert alert-danger">
                                    <h4><i class="fas fa-exclamation-triangle"></i> Overdue Vaccinations</h4>
                                    {% for vaccination in overdue_vaccinations %}
                                        <div class="alert-item">
                                            {{ vaccination.get_vaccine_type_display }} - Due: {{ vaccination.next_due_date }}
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="health-stats">
                            <div class="health-stat-card">
                                <div class="stat-icon"><i class="fas fa-weight"></i></div>
                                <div class="stat-info">
                                    <div class="stat-value">{{ pet.current_weight|default:"Not recorded" }}{% if pet.current_weight %}kg{% endif %}</div>
                                    <div class="stat-label">Current Weight</div>
                                </div>
                            </div>

                            <div class="health-stat-card">
                                <div class="stat-icon"><i class="fas fa-syringe"></i></div>
                                <div class="stat-info">
                                    <div class="stat-value">{{ pet.vaccinations.count }}</div>
                                    <div class="stat-label">Vaccinations</div>
                                </div>
                            </div>

                            <div class="health-stat-card">
                                <div class="stat-icon"><i class="fas fa-stethoscope"></i></div>
                                <div class="stat-info">
                                    <div class="stat-value">{{ pet.medical_records.count }}</div>
                                    <div class="stat-label">Medical Records</div>
                                </div>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <a href="#" class="btn btn-primary">Log Weight</a>
                            <a href="#" class="btn btn-outline-primary">Add Vaccination</a>
                            <a href="{% url 'add-medical-record' pk=pet.pk %}" class="btn btn-outline-primary">Add Medical Record</a>
                        </div>
                    </div>
                </div>

                <!-- Growth & Milestones Tab -->
                <div id="growth" class="tab-pane">
                    <div class="growth-dashboard">
                        {% if recent_growth %}
                            <div class="growth-chart-section">
                                <h4>Recent Growth Records</h4>
                                <div class="growth-records">
                                    {% for record in recent_growth %}
                                        <div class="growth-record">
                                            <div class="growth-date">{{ record.date_recorded|date:"M j, Y" }}</div>
                                            <div class="growth-weight">{{ record.weight }}kg</div>
                                            {% if record.height %}
                                                <div class="growth-height">{{ record.height }}cm tall</div>
                                            {% endif %}
                                            {% if record.notes %}
                                                <div class="growth-notes">{{ record.notes }}</div>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <div class="milestones-section">
                            <h4>Recent Milestones</h4>
                            {% if pet.milestones.all %}
                                <div class="milestones">
                                    {% for milestone in pet.milestones.all|slice:":3" %}
                                        <div class="milestone">
                                            <div class="milestone-type">{{ milestone.get_milestone_type_display }}</div>
                                            <div class="milestone-title">{{ milestone.title }}</div>
                                            <div class="milestone-date">{{ milestone.date_achieved|date:"M j, Y" }}</div>
                                            {% if milestone.age_at_milestone %}
                                                <div class="milestone-age">Age: {{ milestone.age_at_milestone }}</div>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p>No milestones recorded yet.</p>
                            {% endif %}
                        </div>

                        <div class="quick-actions">
                            <a href="#" class="btn btn-primary">Record Growth</a>
                            <a href="#" class="btn btn-outline-primary">Add Milestone</a>
                        </div>
                    </div>
                </div>

                <!-- Reminders Tab -->
                <div id="reminders" class="tab-pane">
                    <div class="reminders-dashboard">
                        {% if upcoming_reminders %}
                            <div class="upcoming-reminders">
                                <h4>Upcoming Reminders</h4>
                                {% for reminder in upcoming_reminders %}
                                    <div class="reminder-card {% if reminder.is_overdue %}overdue{% endif %}">
                                        <div class="reminder-header">
                                            <div class="reminder-title">{{ reminder.title }}</div>
                                            <div class="reminder-date">{{ reminder.due_date|date:"M j, Y g:i A" }}</div>
                                        </div>
                                        <div class="reminder-type">{{ reminder.get_reminder_type_display }}</div>
                                        {% if reminder.description %}
                                            <div class="reminder-description">{{ reminder.description }}</div>
                                        {% endif %}
                                        <div class="reminder-actions">
                                            <button class="btn btn-sm btn-success">Mark Complete</button>
                                            <button class="btn btn-sm btn-outline-secondary">Edit</button>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="empty-state">
                                <p>No upcoming reminders.</p>
                            </div>
                        {% endif %}

                        <div class="quick-actions">
                            <a href="#" class="btn btn-primary">Add Reminder</a>
                            <a href="#" class="btn btn-outline-primary">View All Reminders</a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Pet Inquiry Modal -->
<div class="pet-inquiry-modal" id="pet-inquiry-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">
                {% if pet.is_for_adoption %}
                    Inquire About {{ pet.name }}
                {% else %}
                    Contact About {{ pet.name }}
                {% endif %}
            </h3>
            <button type="button" class="modal-close" id="modal-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <div class="pet-inquiry-preview">
                <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="inquiry-pet-image">
                <div class="inquiry-pet-info">
                    <h4>{{ pet.name }}</h4>
                    <p>{{ pet.breed.name|default:pet.category.name }}</p>
                    {% if pet.is_for_adoption and pet.adoption_price %}
                        <div class="inquiry-price">${{ pet.adoption_price }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="inquiry-message-preview">
                <h5>Your message will be:</h5>
                <div class="message-preview" id="message-preview">
                    {% if pet.is_for_adoption and pet.adoption_price %}
                        Hi! I'm interested in adopting {{ pet.name }} ({{ pet.breed.name|default:pet.category.name }}). I saw they're available for ${{ pet.adoption_price }}. Could you please let me know if {{ pet.name }} is still available and provide more details about the adoption process?
                    {% elif pet.is_for_adoption %}
                        Hi! I'm interested in adopting {{ pet.name }} ({{ pet.breed.name|default:pet.category.name }}). Could you please let me know if {{ pet.name }} is still available and provide more details about the adoption process?
                    {% else %}
                        Hi! I'm interested in your pet {{ pet.name }} ({{ pet.breed.name|default:pet.category.name }}). Could you please provide more information about {{ pet.name }}?
                    {% endif %}
                </div>
            </div>

            <div class="inquiry-actions">
                <p class="inquiry-note">This message will be sent to {{ pet.owner.username }} and start a conversation.</p>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-outline" id="modal-cancel-btn">Cancel</button>
            <button type="button" class="btn btn-primary" id="send-inquiry-btn">Send Inquiry</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image gallery
        const mainImage = document.getElementById('main-image');
        const thumbnails = document.querySelectorAll('.pet-thumbnail');

        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Update main image
                mainImage.src = this.getAttribute('data-src');

                // Update active thumbnail
                thumbnails.forEach(thumb => thumb.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Tabs
        const tabLinks = document.querySelectorAll('.tab-link');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all tabs
                tabLinks.forEach(tab => tab.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding tab content
                const target = this.getAttribute('href').substring(1);
                document.getElementById(target).classList.add('active');
            });
        });

        // Pet Inquiry Modal
        const petInquiryBtn = document.getElementById('pet-inquiry-btn');
        const petInquiryModal = document.getElementById('pet-inquiry-modal');
        const modalCloseBtn = document.getElementById('modal-close-btn');
        const modalCancelBtn = document.getElementById('modal-cancel-btn');
        const sendInquiryBtn = document.getElementById('send-inquiry-btn');

        function openModal() {
            petInquiryModal.classList.add('show');
        }

        function closeModal() {
            petInquiryModal.classList.remove('show');
        }

        if (petInquiryBtn) {
            petInquiryBtn.addEventListener('click', openModal);
        }

        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', closeModal);
        }

        if (modalCancelBtn) {
            modalCancelBtn.addEventListener('click', closeModal);
        }

        // Close modal when clicking outside
        petInquiryModal.addEventListener('click', function(e) {
            if (e.target === petInquiryModal) {
                closeModal();
            }
        });

        // Send pet inquiry
        if (sendInquiryBtn) {
            sendInquiryBtn.addEventListener('click', function() {
                const petId = petInquiryBtn.getAttribute('data-pet-id');

                // Disable button to prevent double submission
                sendInquiryBtn.disabled = true;
                sendInquiryBtn.textContent = 'Sending...';

                fetch(`{% url 'start-pet-inquiry' pet_id=0 %}`.replace('0', petId), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal and redirect to conversation
                        closeModal();
                        window.location.href = `{% url 'conversation-detail' pk=0 %}`.replace('0', data.conversation_id);
                    } else {
                        alert('Error: ' + data.error);
                        sendInquiryBtn.disabled = false;
                        sendInquiryBtn.textContent = 'Send Inquiry';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                    sendInquiryBtn.disabled = false;
                    sendInquiryBtn.textContent = 'Send Inquiry';
                });
            });
        }

        // Wishlist functionality
        const wishlistBtn = document.getElementById('wishlist-btn');
        if (wishlistBtn) {
            // Check if item is already in wishlist
            checkWishlistStatus();

            wishlistBtn.addEventListener('click', function() {
                const itemId = this.getAttribute('data-item-id');
                const itemType = this.getAttribute('data-item-type');

                // Disable button during request
                this.disabled = true;

                fetch('{% url "toggle-wishlist" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: `item_id=${itemId}&item_type=${itemType}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateWishlistButton(data.in_wishlist);
                    } else {
                        alert('Error: ' + data.error);
                    }
                    this.disabled = false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating wishlist.');
                    this.disabled = false;
                });
            });
        }

        function checkWishlistStatus() {
            // Get initial status from backend
            const inWishlist = {% if in_wishlist %}true{% else %}false{% endif %};
            updateWishlistButton(inWishlist);
        }

        function updateWishlistButton(inWishlist) {
            const wishlistBtn = document.getElementById('wishlist-btn');
            const wishlistText = wishlistBtn.querySelector('.wishlist-text');
            const icon = wishlistBtn.querySelector('i');

            if (inWishlist) {
                wishlistBtn.classList.add('in-wishlist');
                wishlistText.textContent = 'Remove from Wishlist';
                icon.className = 'fas fa-heart-broken';
            } else {
                wishlistBtn.classList.remove('in-wishlist');
                wishlistText.textContent = 'Add to Wishlist';
                icon.className = 'fas fa-heart';
            }
        }

        // CSRF token helper function
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}

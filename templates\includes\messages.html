{% load static %}

<!-- Django Messages Display -->
{% if messages %}
<div class="django-messages" id="django-messages">
    {% for message in messages %}
        <div class="django-message {{ message.tags }}" data-message-id="{{ forloop.counter }}">
            <div class="django-message-icon">
                {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle"></i>
                {% elif message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle"></i>
                {% else %}
                    <i class="fas fa-bell"></i>
                {% endif %}
            </div>
            <div class="django-message-text">{{ message }}</div>
            <button type="button" class="django-message-close" onclick="closeMessage(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    {% endfor %}
</div>

<script>
// Message handling functions
function closeMessage(button) {
    const message = button.closest('.django-message');
    message.style.animation = 'fadeOut 0.3s ease-out';
    setTimeout(() => {
        message.remove();
        // Check if no more messages, hide container
        const container = document.getElementById('django-messages');
        if (container && container.children.length === 0) {
            container.style.display = 'none';
        }
    }, 300);
}

// Auto-hide messages after delay
document.addEventListener('DOMContentLoaded', function() {
    const messages = document.querySelectorAll('.django-message');
    
    messages.forEach((message, index) => {
        const messageText = message.textContent || '';
        
        // Immediately hide notification-related messages (as per user preference)
        if (messageText.includes('Message from') ||
            messageText.includes('Conversation') ||
            messageText.includes('sent you a message') ||
            messageText.includes('interested in') ||
            messageText.includes('responded to your inquiry')) {
            message.style.display = 'none';
            setTimeout(() => {
                message.remove();
            }, 100);
            return;
        }
        
        // Auto-hide other messages after 5 seconds (staggered)
        setTimeout(() => {
            if (message.parentNode) {
                message.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    message.remove();
                    // Check if no more messages, hide container
                    const container = document.getElementById('django-messages');
                    if (container && container.children.length === 0) {
                        container.style.display = 'none';
                    }
                }, 300);
            }
        }, 5000 + (index * 500)); // Stagger the auto-hide by 500ms per message
    });
});
</script>
{% endif %}

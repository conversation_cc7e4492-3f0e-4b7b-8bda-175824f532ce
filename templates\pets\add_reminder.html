{% extends 'base.html' %}

{% block title %}Add Reminder for {{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .reminder-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .reminder-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .reminder-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-2xl);
    }
    
    .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .checkbox-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .checkbox-item input[type="checkbox"] {
        margin: 0;
    }
    
    .reminder-examples {
        background-color: var(--gray-50);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .reminder-examples h4 {
        margin-bottom: var(--spacing-md);
        color: var(--gray-700);
    }
    
    .reminder-examples ul {
        margin: 0;
        padding-left: var(--spacing-lg);
    }
    
    .reminder-examples li {
        margin-bottom: var(--spacing-xs);
        color: var(--gray-600);
    }
    
    .frequency-info {
        background-color: var(--blue-50);
        border: 1px solid var(--blue-200);
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        margin-top: var(--spacing-sm);
    }
    
    .frequency-info p {
        margin: 0;
        font-size: var(--font-sm);
        color: var(--blue-700);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="reminder-form-container">
        <div class="reminder-form-header">
            <h1>Add Reminder for {{ pet.name }}</h1>
            <p>Set up reminders for important pet care tasks</p>
        </div>
        
        <div class="reminder-form">
            <form method="post">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Reminder Details</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.reminder_type.id_for_label }}" class="form-label">Reminder Type</label>
                            {{ form.reminder_type }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.due_date.id_for_label }}" class="form-label">Due Date & Time</label>
                            {{ form.due_date }}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.title.id_for_label }}" class="form-label">Title</label>
                        {{ form.title }}
                        <small class="form-text">Brief title for this reminder</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description (Optional)</label>
                        {{ form.description }}
                        <small class="form-text">Additional details about this reminder</small>
                    </div>
                </div>
                
                <div class="reminder-examples">
                    <h4>Reminder Examples by Type:</h4>
                    <ul>
                        <li><strong>Vaccination:</strong> Annual rabies shot, DHPP booster</li>
                        <li><strong>Checkup:</strong> Annual vet visit, dental cleaning</li>
                        <li><strong>Medication:</strong> Heartworm prevention, flea treatment</li>
                        <li><strong>Grooming:</strong> Nail trimming, bath time, professional grooming</li>
                        <li><strong>Exercise:</strong> Daily walk, playtime, training session</li>
                        <li><strong>Weight Check:</strong> Monthly weigh-in, diet monitoring</li>
                    </ul>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Recurrence Settings</h2>
                    
                    <div class="form-group">
                        <label for="{{ form.frequency.id_for_label }}" class="form-label">Frequency</label>
                        {{ form.frequency }}
                    </div>
                    
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            {{ form.is_recurring }}
                            <label for="{{ form.is_recurring.id_for_label }}">Make this a recurring reminder</label>
                        </div>
                        
                        <div class="checkbox-item">
                            {{ form.notification_enabled }}
                            <label for="{{ form.notification_enabled.id_for_label }}">Enable notifications for this reminder</label>
                        </div>
                    </div>
                    
                    <div class="frequency-info">
                        <p><strong>Note:</strong> If you select "Make this a recurring reminder", a new reminder will be automatically created based on the frequency when you mark this one as complete.</p>
                    </div>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Add Reminder</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default due date to tomorrow
        const dueDateInput = document.querySelector('input[name="due_date"]');
        if (dueDateInput && !dueDateInput.value) {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(9, 0, 0, 0); // Set to 9 AM
            
            // Format for datetime-local input
            const year = tomorrow.getFullYear();
            const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
            const day = String(tomorrow.getDate()).padStart(2, '0');
            const hours = String(tomorrow.getHours()).padStart(2, '0');
            const minutes = String(tomorrow.getMinutes()).padStart(2, '0');
            
            dueDateInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
        }
        
        // Auto-populate title based on reminder type
        const reminderTypeSelect = document.querySelector('select[name="reminder_type"]');
        const titleInput = document.querySelector('input[name="title"]');
        
        if (reminderTypeSelect && titleInput) {
            reminderTypeSelect.addEventListener('change', function() {
                if (!titleInput.value) { // Only auto-populate if title is empty
                    const selectedOption = this.options[this.selectedIndex];
                    const reminderType = selectedOption.text;
                    
                    // Generate a default title based on the type
                    switch (this.value) {
                        case 'vaccination':
                            titleInput.value = 'Vaccination Due';
                            break;
                        case 'checkup':
                            titleInput.value = 'Veterinary Checkup';
                            break;
                        case 'medication':
                            titleInput.value = 'Medication Time';
                            break;
                        case 'grooming':
                            titleInput.value = 'Grooming Appointment';
                            break;
                        case 'feeding':
                            titleInput.value = 'Feeding Time';
                            break;
                        case 'exercise':
                            titleInput.value = 'Exercise Time';
                            break;
                        case 'training':
                            titleInput.value = 'Training Session';
                            break;
                        case 'weight_check':
                            titleInput.value = 'Weight Check';
                            break;
                        default:
                            titleInput.value = reminderType;
                    }
                }
            });
        }
    });
</script>
{% endblock %>

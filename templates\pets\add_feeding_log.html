{% extends 'base.html' %}

{% block title %}Log Feeding for {{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .feeding-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .feeding-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .feeding-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-2xl);
    }
    
    .feeding-tips {
        background-color: var(--green-50);
        border: 1px solid var(--green-200);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .feeding-tips h4 {
        margin-bottom: var(--spacing-md);
        color: var(--green-700);
    }
    
    .feeding-tips ul {
        margin: 0;
        padding-left: var(--spacing-lg);
    }
    
    .feeding-tips li {
        margin-bottom: var(--spacing-xs);
        color: var(--green-600);
    }
    
    .amount-examples {
        background-color: var(--gray-50);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-top: var(--spacing-sm);
    }
    
    .amount-examples p {
        margin: 0;
        font-size: var(--font-sm);
        color: var(--gray-600);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="feeding-form-container">
        <div class="feeding-form-header">
            <h1>Log Feeding for {{ pet.name }}</h1>
            <p>Track your pet's meals and eating habits</p>
        </div>
        
        <div class="feeding-form">
            <form method="post">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Feeding Details</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.date_time.id_for_label }}" class="form-label">Date & Time</label>
                            {{ form.date_time }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.meal_type.id_for_label }}" class="form-label">Meal Type</label>
                            {{ form.meal_type }}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.food_type.id_for_label }}" class="form-label">Food Type/Brand</label>
                        {{ form.food_type }}
                        <small class="form-text">What type or brand of food was given?</small>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.amount_given.id_for_label }}" class="form-label">Amount Given</label>
                            {{ form.amount_given }}
                            <div class="amount-examples">
                                <p><strong>Examples:</strong> 1 cup, 200g, 2 scoops, 1/2 can</p>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.amount_consumed.id_for_label }}" class="form-label">Amount Consumed (Optional)</label>
                            {{ form.amount_consumed }}
                            <small class="form-text">How much was actually eaten?</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes (Optional)</label>
                        {{ form.notes }}
                        <small class="form-text">Appetite, behavior, any observations during feeding</small>
                    </div>
                </div>
                
                <div class="feeding-tips">
                    <h4>Feeding Log Tips:</h4>
                    <ul>
                        <li>Record both the amount given and amount consumed to track appetite</li>
                        <li>Note any changes in eating behavior or preferences</li>
                        <li>Include treats and snacks in your logs</li>
                        <li>Track feeding times to establish consistent routines</li>
                        <li>Note any digestive issues or food reactions</li>
                    </ul>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Log Feeding</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set current date and time as default
        const dateTimeInput = document.querySelector('input[name="date_time"]');
        if (dateTimeInput && !dateTimeInput.value) {
            const now = new Date();
            
            // Format for datetime-local input
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            
            dateTimeInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
        }
        
        // Auto-suggest meal type based on current time
        const mealTypeSelect = document.querySelector('select[name="meal_type"]');
        if (mealTypeSelect && !mealTypeSelect.value) {
            const currentHour = new Date().getHours();
            
            if (currentHour >= 5 && currentHour < 10) {
                mealTypeSelect.value = 'breakfast';
            } else if (currentHour >= 11 && currentHour < 15) {
                mealTypeSelect.value = 'lunch';
            } else if (currentHour >= 17 && currentHour < 21) {
                mealTypeSelect.value = 'dinner';
            } else {
                mealTypeSelect.value = 'snack';
            }
        }
        
        // Auto-populate amount consumed when amount given changes
        const amountGivenInput = document.querySelector('input[name="amount_given"]');
        const amountConsumedInput = document.querySelector('input[name="amount_consumed"]');
        
        if (amountGivenInput && amountConsumedInput) {
            amountGivenInput.addEventListener('blur', function() {
                if (this.value && !amountConsumedInput.value) {
                    // Default to assuming all food was consumed
                    amountConsumedInput.value = this.value;
                }
            });
        }
    });
</script>
{% endblock %>

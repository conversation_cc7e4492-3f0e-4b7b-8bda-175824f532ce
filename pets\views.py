from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse
from .models import (
    Pet, PetCategory, PetBreed, PetGallery, PetMedicalRecord, PetReminder,
    Vaccination, GrowthRecord, Milestone, ExerciseLog, FeedingLog
)
from .forms import (
    PetForm, PetGalleryForm, PetMedicalRecordForm, VaccinationForm,
    GrowthRecordForm, MilestoneForm, PetReminderForm, ExerciseLogForm, FeedingLogForm
)


class PetListView(ListView):
    """View for listing pets available for adoption (public view)"""
    model = Pet
    template_name = 'pets/pet_list.html'
    context_object_name = 'pets'
    paginate_by = 12

    def get_queryset(self):
        # Only show pets available for adoption in public listing
        queryset = Pet.objects.filter(is_for_adoption=True)

        # Filter by category if provided
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__name__iexact=category)

        # Additional filters for adoption pets
        breed = self.request.GET.get('breed')
        if breed:
            queryset = queryset.filter(breed__id=breed)

        gender = self.request.GET.get('gender')
        if gender:
            queryset = queryset.filter(gender=gender)

        size = self.request.GET.get('size')
        if size:
            queryset = queryset.filter(size=size)

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = PetCategory.objects.all()
        context['breeds'] = PetBreed.objects.all()
        context['is_adoption_listing'] = True
        return context


class MyPetsView(LoginRequiredMixin, ListView):
    """View for pet owners to see all their pets"""
    model = Pet
    template_name = 'pets/my_pets.html'
    context_object_name = 'pets'
    paginate_by = 12

    def get_queryset(self):
        # Show all pets owned by the current user
        return Pet.objects.filter(owner=self.request.user).order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_pets = self.get_queryset()

        # Add summary statistics
        context['total_pets'] = user_pets.count()
        context['pets_for_adoption'] = user_pets.filter(is_for_adoption=True).count()
        context['recent_pets'] = user_pets[:3]

        # Add upcoming reminders
        from datetime import datetime, timedelta
        upcoming_reminders = PetReminder.objects.filter(
            pet__owner=self.request.user,
            is_completed=False,
            due_date__lte=datetime.now() + timedelta(days=7)
        ).order_by('due_date')[:5]
        context['upcoming_reminders'] = upcoming_reminders

        # Add overdue vaccinations
        overdue_vaccinations = []
        for pet in user_pets:
            for vaccination in pet.vaccinations.all():
                if vaccination.is_overdue:
                    overdue_vaccinations.append(vaccination)
        context['overdue_vaccinations'] = overdue_vaccinations[:5]

        return context


class PetDetailView(DetailView):
    """View for displaying pet details"""
    model = Pet
    template_name = 'pets/pet_detail.html'
    context_object_name = 'pet'

    def get_object(self, queryset=None):
        """Override to check pet visibility permissions"""
        pet = super().get_object(queryset)

        # If pet is not for adoption, only owner can view it
        if not pet.is_for_adoption:
            if not self.request.user.is_authenticated or self.request.user != pet.owner:
                from django.http import Http404
                raise Http404("Pet not found or you don't have permission to view it.")

        return pet

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['gallery'] = self.object.gallery.all()
        context['is_owner'] = self.request.user == self.object.owner

        # Check if user is following this pet (only for adoption pets)
        if self.request.user.is_authenticated and self.object.is_for_adoption:
            context['is_following'] = self.object.followers.filter(id=self.request.user.id).exists()

            # Check if pet is in user's wishlist
            from users.utils import is_in_wishlist
            context['in_wishlist'] = is_in_wishlist(self.request.user, self.object)

        # Add owner-specific data if viewing own pet
        if context['is_owner']:
            # Recent medical records
            context['recent_medical_records'] = self.object.medical_records.all()[:3]

            # Upcoming reminders
            from datetime import datetime, timedelta
            context['upcoming_reminders'] = self.object.reminders.filter(
                is_completed=False,
                due_date__lte=datetime.now() + timedelta(days=7)
            ).order_by('due_date')[:3]

            # Recent growth records
            context['recent_growth'] = self.object.growth_records.all()[:3]

            # Overdue vaccinations
            overdue_vaccinations = [v for v in self.object.vaccinations.all() if v.is_overdue]
            context['overdue_vaccinations'] = overdue_vaccinations[:3]

        return context


class PetCreateView(LoginRequiredMixin, CreateView):
    """View for creating a new pet"""
    model = Pet
    form_class = PetForm
    template_name = 'pets/pet_form.html'

    def form_valid(self, form):
        form.instance.owner = self.request.user
        messages.success(self.request, 'Pet profile created successfully!')
        return super().form_valid(form)


class PetUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View for updating a pet"""
    model = Pet
    form_class = PetForm
    template_name = 'pets/pet_form.html'

    def form_valid(self, form):
        messages.success(self.request, 'Pet profile updated successfully!')
        return super().form_valid(form)

    def test_func(self):
        pet = self.get_object()
        return self.request.user == pet.owner


class PetDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View for deleting a pet"""
    model = Pet
    template_name = 'pets/pet_confirm_delete.html'
    success_url = reverse_lazy('pet-list')

    def test_func(self):
        pet = self.get_object()
        return self.request.user == pet.owner

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Pet profile deleted successfully!')
        return super().delete(request, *args, **kwargs)


@login_required
def follow_pet(request, pk):
    """View for following/unfollowing a pet"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user == pet.owner:
        messages.error(request, "You cannot follow your own pet.")
        return redirect('pet-detail', pk=pk)

    if pet.followers.filter(id=request.user.id).exists():
        pet.followers.remove(request.user)
        messages.success(request, f"You have unfollowed {pet.name}.")
    else:
        pet.followers.add(request.user)
        messages.success(request, f"You are now following {pet.name}.")

    return redirect('pet-detail', pk=pk)


@login_required
def add_pet_photo(request, pk):
    """View for adding photos to pet gallery"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add photos to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = PetGalleryForm(request.POST, request.FILES)
        if form.is_valid():
            photo = form.save(commit=False)
            photo.pet = pet
            photo.save()
            messages.success(request, 'Photo added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = PetGalleryForm()

    return render(request, 'pets/add_pet_photo.html', {'form': form, 'pet': pet})


@login_required
def add_medical_record(request, pk):
    """View for adding medical records to a pet"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add medical records to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = PetMedicalRecordForm(request.POST, request.FILES)
        if form.is_valid():
            record = form.save(commit=False)
            record.pet = pet
            record.save()
            messages.success(request, 'Medical record added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = PetMedicalRecordForm()

    return render(request, 'pets/add_medical_record.html', {'form': form, 'pet': pet})


def load_breeds(request):
    """AJAX view for loading breeds based on selected category"""
    category_id = request.GET.get('category')
    breeds = PetBreed.objects.filter(category_id=category_id).order_by('name')
    return JsonResponse({'breeds': list(breeds.values('id', 'name'))}, safe=False)


# Health Tracking Views

@login_required
def add_vaccination(request, pk):
    """View for adding vaccination records"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add vaccination records to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = VaccinationForm(request.POST, request.FILES)
        if form.is_valid():
            vaccination = form.save(commit=False)
            vaccination.pet = pet
            vaccination.save()
            messages.success(request, 'Vaccination record added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = VaccinationForm()

    return render(request, 'pets/add_vaccination.html', {'form': form, 'pet': pet})


@login_required
def add_growth_record(request, pk):
    """View for adding growth records"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add growth records to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = GrowthRecordForm(request.POST, request.FILES)
        if form.is_valid():
            growth_record = form.save(commit=False)
            growth_record.pet = pet
            growth_record.save()
            messages.success(request, 'Growth record added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = GrowthRecordForm()

    return render(request, 'pets/add_growth_record.html', {'form': form, 'pet': pet})


@login_required
def add_milestone(request, pk):
    """View for adding milestones"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add milestones to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = MilestoneForm(request.POST, request.FILES)
        if form.is_valid():
            milestone = form.save(commit=False)
            milestone.pet = pet
            milestone.save()
            messages.success(request, 'Milestone added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = MilestoneForm()

    return render(request, 'pets/add_milestone.html', {'form': form, 'pet': pet})


@login_required
def add_reminder(request, pk):
    """View for adding reminders"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add reminders to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = PetReminderForm(request.POST)
        if form.is_valid():
            reminder = form.save(commit=False)
            reminder.pet = pet
            reminder.save()
            messages.success(request, 'Reminder added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = PetReminderForm()

    return render(request, 'pets/add_reminder.html', {'form': form, 'pet': pet})


@login_required
def add_exercise_log(request, pk):
    """View for adding exercise logs"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add exercise logs to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = ExerciseLogForm(request.POST)
        if form.is_valid():
            exercise_log = form.save(commit=False)
            exercise_log.pet = pet
            exercise_log.save()
            messages.success(request, 'Exercise log added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = ExerciseLogForm()

    return render(request, 'pets/add_exercise_log.html', {'form': form, 'pet': pet})


@login_required
def add_feeding_log(request, pk):
    """View for adding feeding logs"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add feeding logs to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = FeedingLogForm(request.POST)
        if form.is_valid():
            feeding_log = form.save(commit=False)
            feeding_log.pet = pet
            feeding_log.save()
            messages.success(request, 'Feeding log added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = FeedingLogForm()

    return render(request, 'pets/add_feeding_log.html', {'form': form, 'pet': pet})


# AJAX Views for Quick Actions

@login_required
def quick_weight_log(request, pk):
    """AJAX view for quickly logging pet weight"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    if request.method == 'POST':
        weight = request.POST.get('weight')
        date = request.POST.get('date')

        if weight and date:
            try:
                from datetime import datetime
                growth_record = GrowthRecord.objects.create(
                    pet=pet,
                    weight=float(weight),
                    date_recorded=datetime.strptime(date, '%Y-%m-%d').date()
                )
                return JsonResponse({
                    'success': True,
                    'message': 'Weight logged successfully!',
                    'weight': str(growth_record.weight),
                    'date': growth_record.date_recorded.strftime('%Y-%m-%d')
                })
            except (ValueError, TypeError):
                return JsonResponse({'success': False, 'error': 'Invalid data'})

    return JsonResponse({'success': False, 'error': 'Invalid request'})


@login_required
def complete_reminder(request, pk, reminder_id):
    """AJAX view for marking reminders as complete"""
    pet = get_object_or_404(Pet, pk=pk)
    reminder = get_object_or_404(PetReminder, pk=reminder_id, pet=pet)

    if request.user != pet.owner:
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    if request.method == 'POST':
        reminder.is_completed = True
        reminder.save()
        return JsonResponse({
            'success': True,
            'message': 'Reminder marked as complete!'
        })

    return JsonResponse({'success': False, 'error': 'Invalid request'})
